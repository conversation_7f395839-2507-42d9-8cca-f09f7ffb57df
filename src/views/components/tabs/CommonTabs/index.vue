<template>
  <div class="wrap">
    <el-tabs v-model="activeName" type="border-card" class="bi-tabs">
      <el-tab-pane
        v-if="!hideList.includes(1)"
        name="1"
        key="1"
        class="custom-scrollbar"
        label="数据区"
      >
         <div class="search-area-sticky" :style="{ top: 0 + 'px' }">
          <slot name="searchArea"></slot>
        </div>
        <slot></slot>
      </el-tab-pane>
      <el-tab-pane
        v-if="!hideList.includes(2)"
        name="2"
        key="2"
        class="custom-scrollbar"
        label="新闻区"
      >
        <NewS :key="activeName" />
      </el-tab-pane>
      <el-tab-pane
        v-if="!hideList.includes(3)"
        name="3"
        key="3"
        class="custom-scrollbar"
        label="报告区"
      >
        <ReportList :key="activeName" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import ReportList from '@/views/components/tabs/ReportList.vue'
import NewS from '@/views/components/tabs/NewS.vue'
import { inject } from 'vue'

const props = defineProps({
  // label
  hideList: {
    type: Array,
    default: []
  },
  title: {
    type: String,
    require: false,
    default: '数据区'
  },
  title2: {
    type: String,
    require: false,
    default: '新闻区'
  },
  active: {
    type: String,
    require: false,
    default: '1'
  }
})

const activeName = ref(props.active)
</script>

<style lang="scss" scoped>
@import '@/assets/styles/bi/variables.module.scss';

.wrap {
  height: $bi-main-height;
  :deep(.el-tabs) {
    border-radius: 8px;
    height: 100%;
    background: linear-gradient(0deg, #d2e6fc 0%, rgba(210, 230, 252, 0.5) 100%);
    .el-tab-pane {
      height: 100%;
      overflow: auto;
    }
  }
  :deep(.el-tabs--border-card) {
    & > .el-tabs__content {
      padding: 0 16px 16px 16px;
    }
  }
}
.search-area-sticky {
  position: sticky;
  z-index: 10; /* 确保固钉元素在其他内容之上 */
  padding-bottom: 16px; /* 保持与原ElAffix的间距 */
}
</style>
