<template>
  
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <!-- <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    /> -->
    <sidebar
      class="sidebar"
      :localDevice="device"
      :mobileExpend="mobileExpend"
      @closeDrawer="mobileExpend = false"
      :style="{top: device && hideHeader ? 0 : biHeadHeight, height: device && hideHeader ? '100%' : ''}"
    />
    <div class="main-container-bi main-container-bix" :class="{ 'mobile-main-container-bi': device === 'mobile' }">
      <div v-if="!hideHeader" class="fixed-header">
        <navbar
          @toggleExpand="toggleExpand"
          @toggleHeader="toggleHeader"
          :localDevice="device"
          :mobileExpend="mobileExpend"
        />
      </div>
      <!-- 当header隐藏时显示的切换图标 -->
      <div v-if="hideHeader" class="header-toggle-btn" @click="toggleHeader">
        <el-icon class="toggle-icon">
          <ArrowDown />
        </el-icon>
      </div>
      <app-main />
    </div>
  </div>
</template>

<script setup>

import { useWindowSize } from '@vueuse/core'
import { ArrowDown } from '@element-plus/icons-vue'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar } from './components'
// 导入SCSS变量
import variables from '@/assets/styles/bi/variables.module.scss'
import {provide } from 'vue'

const store = useStore()
const theme = computed(() => store.state.bisettings.theme)
// const sideTheme = computed(() => store.state.settings.sideTheme);
const sidebar = computed(() => store.state.biapp.sidebar)
const device = computed(() => store.state.biapp.device)
const classObj = computed(() => ({
  hideSidebarBi: device.value === 'mobile' ? false : !sidebar.value.opened,
  openSidebarBi: device.value === 'mobile' ? true : sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width, height } = useWindowSize()
const WIDTH = 415 // refer to Bootstrap's responsive design

watchEffect(() => {
  if (device.value === 'mobile' && !sidebar.value.opened) {
    store.dispatch('biapp/toggleSideBar')
  }
  if (width.value - 1 < WIDTH) {
    store.dispatch('biapp/toggleDevice', 'mobile')
    // store.dispatch('biapp/closeSideBar', { withoutAnimation: true })
  } else {
    store.dispatch('biapp/toggleDevice', 'desktop')
  }
})

function handleClickOutside() {
  store.dispatch('biapp/closeSideBar', { withoutAnimation: false })
}

const mobileExpend = ref(false)
const hideHeader = ref(false)

// 从 SCSS 模块中获取变量值
const biHeadHeight = variables.biHeadHeight
// const biMainHeight = variables.biMainHeight

function toggleExpand(ev) {
  mobileExpend.value = ev
}

function toggleHeader() {
  hideHeader.value = !hideHeader.value
  provide('hideHeader',  hideHeader.value)

}
// const settingRef = ref(null);
// function setLayout() {
//   settingRef.value.openSetting();
// }
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
@import '@/assets/styles/bi/variables.module.scss';




.app-wrapper {
  @include clearfix;
  position: relative;
  min-height: 100%;
  width: 100%;
  background: url('@/assets/images/main-bg.png') no-repeat;
  background-size: 100% 100%;
  background-attachment: fixed;
  // &.mobile.openSidebarBi {
  //   position: fixed;
  //   top: 0;
  // }
}

.drawer-bg {
  background: #000;
  opacity: 1;
  width: 100%;
  top: 0;
  left: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9;
  width: 100%;
  // padding-bottom: $bi-layout-margin;
  // background: $bi-base-background-color;
  background-image: url('/src/assets/images/top-bg.png');
  background-repeat: no-repeat;
  // background-size: 100% 100%;
  background-size: cover;
  transition: width 0.28s;
}

// .mobile .fixed-header {
//   width: 100%;
// }

.header-toggle-btn {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 999;
  width: 40px;
  height: 40px;
  background: rgba(41, 112, 218, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s;

  &:hover {
    background: rgba(41, 112, 218, 1);
    transform: scale(1.1);
  }

  .toggle-icon {
    color: white;
    font-size: 18px;
  }
}

</style>
